import React, { useState } from 'react';
import {
  Box,
  TextField,
  Button,
  Alert,
  Chip,
  <PERSON>ack,
  Typography,
  InputAdornment,
} from '@mui/material';
import { CloudDownload, Link as LinkIcon, Analytics } from '@mui/icons-material';
import { Track } from '../types/Track';
import { analyzeSoundCloudSet } from '../services/audioAnalysisService';

interface SoundCloudInputProps {
  onAnalysisStart: () => void;
  onAnalysisComplete: (tracks: Track[]) => void;
  onProgressUpdate: (progress: number) => void;
}

const SoundCloudInput: React.FC<SoundCloudInputProps> = ({
  onAnalysisStart,
  onAnalysisComplete,
  onProgressUpdate,
}) => {
  const [url, setUrl] = useState('');
  const [error, setError] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  const validateSoundCloudUrl = (url: string): boolean => {
    const soundcloudRegex = /^https?:\/\/(www\.)?(soundcloud\.com|snd\.sc)\/.+/;
    return soundcloudRegex.test(url);
  };

  const handleAnalyze = async () => {
    if (!url.trim()) {
      setError('Please enter a SoundCloud URL');
      return;
    }

    if (!validateSoundCloudUrl(url)) {
      setError('Please enter a valid SoundCloud URL');
      return;
    }

    setError('');
    setIsLoading(true);
    onAnalysisStart();

    try {
      const tracks = await analyzeSoundCloudSet(url, onProgressUpdate);
      onAnalysisComplete(tracks);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Analysis failed');
    } finally {
      setIsLoading(false);
    }
  };

  const handleUrlChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setUrl(event.target.value);
    if (error) setError('');
  };

  const exampleUrls = [
    'https://soundcloud.com/example/dj-set-1',
    'https://soundcloud.com/example/house-mix-2023',
    'https://soundcloud.com/example/techno-journey',
  ];

  return (
    <Box>
      <TextField
        fullWidth
        label="SoundCloud DJ Set URL"
        placeholder="https://soundcloud.com/artist/dj-set-name"
        value={url}
        onChange={handleUrlChange}
        error={!!error}
        helperText={error || 'Enter the URL of a SoundCloud DJ set or mix'}
        InputProps={{
          startAdornment: (
            <InputAdornment position="start">
              <LinkIcon color="action" />
            </InputAdornment>
          ),
        }}
        sx={{ mb: 3 }}
      />

      <Box sx={{ display: 'flex', justifyContent: 'center', mb: 3 }}>
        <Button
          variant="contained"
          size="large"
          onClick={handleAnalyze}
          disabled={isLoading || !url.trim()}
          startIcon={isLoading ? <CloudDownload /> : <Analytics />}
          sx={{
            px: 4,
            py: 1.5,
            borderRadius: 3,
            background: 'linear-gradient(45deg, #1976d2 30%, #42a5f5 90%)',
            '&:hover': {
              background: 'linear-gradient(45deg, #1565c0 30%, #1976d2 90%)',
            },
          }}
        >
          {isLoading ? 'Analyzing...' : 'Analyze DJ Set'}
        </Button>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      <Box>
        <Typography variant="body2" color="text.secondary" gutterBottom>
          Try these example URLs:
        </Typography>
        <Stack direction="row" spacing={1} flexWrap="wrap" useFlexGap>
          {exampleUrls.map((exampleUrl, index) => (
            <Chip
              key={index}
              label={`Example ${index + 1}`}
              variant="outlined"
              clickable
              onClick={() => setUrl(exampleUrl)}
              size="small"
            />
          ))}
        </Stack>
      </Box>
    </Box>
  );
};

export default SoundCloudInput;
