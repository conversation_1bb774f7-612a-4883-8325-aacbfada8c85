import React, { useState } from 'react';
import {
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Typography,
  Chip,
  Box,
  IconButton,
  Collapse,
  Button,
  Stack,
  Avatar,
} from '@mui/material';
import {
  KeyboardArrowDown,
  KeyboardArrowUp,
  PlayArrow,
  GetApp,
  Share,
  MusicNote,
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import { Track } from '../types/Track';
import TrackVisualization from './TrackVisualization';
import ExportDialog from './ExportDialog';

interface TrackListProps {
  tracks: Track[];
}

interface TrackRowProps {
  track: Track;
  index: number;
}

const TrackRow: React.FC<TrackRowProps> = ({ track, index }) => {
  const [open, setOpen] = useState(false);

  const formatTime = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const getKeyColor = (key: string): string => {
    const keyColors: { [key: string]: string } = {
      'C': '#FF6B6B', 'C#': '#4ECDC4', 'D': '#45B7D1', 'D#': '#96CEB4',
      'E': '#FFEAA7', 'F': '#DDA0DD', 'F#': '#98D8C8', 'G': '#F7DC6F',
      'G#': '#BB8FCE', 'A': '#85C1E9', 'A#': '#F8C471', 'B': '#82E0AA'
    };
    return keyColors[key] || '#95A5A6';
  };

  const getTempoColor = (tempo: number): string => {
    if (tempo < 100) return '#3498DB'; // Blue for slow
    if (tempo < 130) return '#2ECC71'; // Green for medium
    if (tempo < 150) return '#F39C12'; // Orange for fast
    return '#E74C3C'; // Red for very fast
  };

  return (
    <>
      <TableRow
        component={motion.tr}
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: index * 0.1 }}
        sx={{ '& > *': { borderBottom: 'unset' } }}
      >
        <TableCell>
          <IconButton
            aria-label="expand row"
            size="small"
            onClick={() => setOpen(!open)}
          >
            {open ? <KeyboardArrowUp /> : <KeyboardArrowDown />}
          </IconButton>
        </TableCell>
        <TableCell component="th" scope="row">
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <Avatar sx={{ bgcolor: 'primary.main', width: 32, height: 32 }}>
              <MusicNote fontSize="small" />
            </Avatar>
            <Box>
              <Typography variant="body1" fontWeight={600}>
                {track.songName}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                {track.artistName}
              </Typography>
            </Box>
          </Box>
        </TableCell>
        <TableCell align="center">
          <Chip
            label={`${track.tempo} BPM`}
            size="small"
            sx={{
              bgcolor: getTempoColor(track.tempo),
              color: 'white',
              fontWeight: 600,
            }}
          />
        </TableCell>
        <TableCell align="center">
          <Chip
            label={track.key}
            size="small"
            sx={{
              bgcolor: getKeyColor(track.key),
              color: 'white',
              fontWeight: 600,
            }}
          />
        </TableCell>
        <TableCell align="center">
          <Typography variant="body2">
            {formatTime(track.startTime)} - {formatTime(track.endTime)}
          </Typography>
        </TableCell>
        <TableCell align="center">
          <Typography variant="body2">
            {formatTime(track.duration)}
          </Typography>
        </TableCell>
        <TableCell align="center">
          <Chip
            label={`${Math.round(track.confidence * 100)}%`}
            size="small"
            color={track.confidence > 0.8 ? 'success' : track.confidence > 0.6 ? 'warning' : 'error'}
            variant="outlined"
          />
        </TableCell>
      </TableRow>
      <TableRow>
        <TableCell style={{ paddingBottom: 0, paddingTop: 0 }} colSpan={7}>
          <Collapse in={open} timeout="auto" unmountOnExit>
            <Box sx={{ margin: 1 }}>
              <Typography variant="h6" gutterBottom component="div">
                Track Details
              </Typography>
              <Stack direction="row" spacing={2} sx={{ mb: 2 }}>
                <Button startIcon={<PlayArrow />} size="small">
                  Preview
                </Button>
                <Button startIcon={<Share />} size="small">
                  Share
                </Button>
                <Button startIcon={<GetApp />} size="small">
                  Export
                </Button>
              </Stack>
              {track.genre && (
                <Typography variant="body2" sx={{ mb: 1 }}>
                  <strong>Genre:</strong> {track.genre}
                </Typography>
              )}
              {track.energy && (
                <Typography variant="body2" sx={{ mb: 1 }}>
                  <strong>Energy:</strong> {Math.round(track.energy * 100)}%
                </Typography>
              )}
              {track.danceability && (
                <Typography variant="body2" sx={{ mb: 2 }}>
                  <strong>Danceability:</strong> {Math.round(track.danceability * 100)}%
                </Typography>
              )}
              <TrackVisualization track={track} />
            </Box>
          </Collapse>
        </TableCell>
      </TableRow>
    </>
  );
};

const TrackList: React.FC<TrackListProps> = ({ tracks }) => {
  const [exportDialogOpen, setExportDialogOpen] = useState(false);
  const totalDuration = tracks.reduce((sum, track) => sum + track.duration, 0);
  const averageTempo = Math.round(
    tracks.reduce((sum, track) => sum + track.tempo, 0) / tracks.length
  );

  const formatTime = (seconds: number): string => {
    const hours = Math.floor(seconds / 3600);
    const mins = Math.floor((seconds % 3600) / 60);
    const secs = Math.floor(seconds % 60);

    if (hours > 0) {
      return `${hours}:${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 30 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
    >
      <Paper elevation={3} sx={{ mb: 3 }}>
        <Box sx={{ p: 3 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
            <Typography variant="h5">
              Analysis Results
            </Typography>
            <Button
              variant="outlined"
              startIcon={<GetApp />}
              onClick={() => setExportDialogOpen(true)}
            >
              Export Tracks
            </Button>
          </Box>
          <Stack direction="row" spacing={3} flexWrap="wrap">
            <Typography variant="body1">
              <strong>Total Tracks:</strong> {tracks.length}
            </Typography>
            <Typography variant="body1">
              <strong>Total Duration:</strong> {formatTime(totalDuration)}
            </Typography>
            <Typography variant="body1">
              <strong>Average BPM:</strong> {averageTempo}
            </Typography>
          </Stack>
        </Box>
      </Paper>

      <TableContainer component={Paper} elevation={3}>
        <Table aria-label="track list">
          <TableHead>
            <TableRow>
              <TableCell />
              <TableCell><strong>Track</strong></TableCell>
              <TableCell align="center"><strong>Tempo</strong></TableCell>
              <TableCell align="center"><strong>Key</strong></TableCell>
              <TableCell align="center"><strong>Time Range</strong></TableCell>
              <TableCell align="center"><strong>Duration</strong></TableCell>
              <TableCell align="center"><strong>Confidence</strong></TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {tracks.map((track, index) => (
              <TrackRow key={track.id} track={track} index={index} />
            ))}
          </TableBody>
        </Table>
      </TableContainer>

      <ExportDialog
        open={exportDialogOpen}
        onClose={() => setExportDialogOpen(false)}
        tracks={tracks}
      />
    </motion.div>
  );
};

export default TrackList;
