export interface Track {
  id: string;
  songName: string;
  artistName: string;
  tempo: number;
  key: string;
  startTime: number; // in seconds
  endTime: number; // in seconds
  duration: number; // in seconds
  confidence: number; // 0-1 confidence score for identification
  genre?: string;
  energy?: number; // 0-1 energy level
  danceability?: number; // 0-1 danceability score
  waveformUrl?: string;
  thumbnailUrl?: string;
}

export interface AnalysisResult {
  tracks: Track[];
  totalDuration: number;
  averageTempo: number;
  dominantKey: string;
  energyProfile: number[];
  mixQuality: number; // 0-1 score for mix quality
}

export interface SoundCloudTrackInfo {
  id: string;
  title: string;
  user: {
    username: string;
  };
  duration: number;
  stream_url: string;
  waveform_url: string;
  artwork_url: string;
  genre: string;
  tag_list: string;
}
