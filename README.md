# SoundCloud DJ Set Analyzer

A modern React web application for analyzing SoundCloud DJ sets with track identification, tempo detection, and key analysis.

## Features

- 🎵 **SoundCloud Integration**: Analyze DJ sets directly from SoundCloud URLs
- 🎯 **Track Identification**: Automatically identify individual tracks within DJ sets
- 🎼 **Tempo Analysis**: Detect BPM (beats per minute) for each track
- 🎹 **Key Detection**: Identify musical keys and harmonies
- 🌓 **Dark/Light Mode**: Toggle between dark and light themes
- 📊 **Visualizations**: Interactive charts and waveform displays
- 📱 **Responsive Design**: Works on desktop, tablet, and mobile devices
- 📤 **Export Options**: Export track lists in JSON, CSV, or TXT formats

## Prerequisites

Before running this application, make sure you have:

- **Node.js** (version 16 or higher)
- **npm** or **yarn** package manager
- A **SoundCloud API Client ID** (optional, for real API integration)

## Installation

1. **Install Node.js** (if not already installed):
   - Download from [nodejs.org](https://nodejs.org/)
   - Or use a package manager like <PERSON><PERSON> on Windows: `choco install nodejs`

2. **<PERSON>lone or navigate to the project directory**:
   ```bash
   cd "c:\Users\<USER>\Documents\Vs Code Augment"
   ```

3. **Install dependencies**:
   ```bash
   npm install
   ```

4. **Set up environment variables** (optional):
   Create a `.env` file in the root directory:
   ```
   REACT_APP_SOUNDCLOUD_CLIENT_ID=your_soundcloud_client_id_here
   ```

## Running the Application

1. **Start the development server**:
   ```bash
   npm start
   ```

2. **Open your browser** and navigate to:
   ```
   http://localhost:3000
   ```

## Usage

1. **Enter a SoundCloud URL**: Paste the URL of a DJ set or mix from SoundCloud
2. **Click "Analyze DJ Set"**: The application will process the audio and identify tracks
3. **View Results**: See the identified tracks with tempo, key, and timing information
4. **Explore Details**: Click on any track row to see detailed visualizations
5. **Toggle Theme**: Use the switch in the top-right corner to change between light and dark modes
6. **Export Data**: Use the export buttons to save track information

## Project Structure

```
src/
├── components/           # React components
│   ├── SoundCloudInput.tsx
│   ├── TrackList.tsx
│   ├── AnalysisProgress.tsx
│   └── TrackVisualization.tsx
├── contexts/            # React contexts
│   └── ThemeContext.tsx
├── services/            # API and analysis services
│   └── audioAnalysisService.ts
├── types/               # TypeScript type definitions
│   └── Track.ts
├── App.tsx              # Main application component
└── index.tsx            # Application entry point
```

## Technologies Used

- **React 18** with TypeScript
- **Material-UI (MUI)** for UI components
- **Framer Motion** for animations
- **Recharts** for data visualization
- **Tone.js** for audio processing (planned)
- **WaveSurfer.js** for waveform visualization (planned)

## Current Limitations

This is a demonstration version with the following limitations:

- **Mock Data**: Currently uses simulated track identification and analysis
- **SoundCloud API**: Requires a valid API key for real SoundCloud integration
- **Audio Processing**: Advanced audio analysis features are simulated
- **Track Database**: No real music recognition database integration

## Future Enhancements

- 🔌 **Real Audio Analysis**: Integrate with actual audio processing libraries
- 🎵 **Music Recognition**: Connect to services like Shazam or AcoustID
- 🎚️ **Advanced Mixing Analysis**: Detect transitions, crossfades, and mix quality
- 📱 **Mobile App**: React Native version for mobile devices
- 🔊 **Audio Playback**: Built-in audio player with synchronized visualization
- 🎪 **Playlist Generation**: Create Spotify/Apple Music playlists from analyzed tracks

## Development

### Available Scripts

- `npm start` - Start development server
- `npm build` - Build for production
- `npm test` - Run tests
- `npm run lint` - Run ESLint
- `npm run lint:fix` - Fix ESLint issues

### Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the MIT License.

## Support

For issues and questions:
- Check the GitHub Issues page
- Review the documentation
- Contact the development team

---

**Note**: This application is for educational and demonstration purposes. Make sure to comply with SoundCloud's Terms of Service when using their API.
