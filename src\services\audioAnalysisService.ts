import { Track, SoundCloudTrackInfo } from '../types/Track';

// SoundCloud API configuration
const SOUNDCLOUD_CLIENT_ID = process.env.REACT_APP_SOUNDCLOUD_CLIENT_ID || 'your_client_id_here';
const SOUNDCLOUD_API_BASE = 'https://api.soundcloud.com';

// Mock data for demonstration purposes
const MOCK_TRACKS: Track[] = [
  {
    id: '1',
    songName: 'Midnight City',
    artistName: 'M83',
    tempo: 128,
    key: 'F#m',
    startTime: 0,
    endTime: 240,
    duration: 240,
    confidence: 0.92,
    genre: 'Electronic',
    energy: 0.85,
    danceability: 0.78,
  },
  {
    id: '2',
    songName: 'One More Time',
    artistName: 'Daft Punk',
    tempo: 123,
    key: 'Em',
    startTime: 240,
    endTime: 480,
    duration: 240,
    confidence: 0.88,
    genre: 'House',
    energy: 0.92,
    danceability: 0.95,
  },
  {
    id: '3',
    songName: 'Strobe',
    artistName: 'Deadmau5',
    tempo: 128,
    key: 'C#m',
    startTime: 480,
    endTime: 840,
    duration: 360,
    confidence: 0.95,
    genre: 'Progressive House',
    energy: 0.75,
    danceability: 0.82,
  },
  {
    id: '4',
    songName: 'Levels',
    artistName: 'Avicii',
    tempo: 126,
    key: 'C#m',
    startTime: 840,
    endTime: 1080,
    duration: 240,
    confidence: 0.90,
    genre: 'Progressive House',
    energy: 0.88,
    danceability: 0.89,
  },
];

/**
 * Extracts SoundCloud track ID from URL
 */
const extractTrackId = (url: string): string | null => {
  const patterns = [
    /soundcloud\.com\/([^\/]+)\/([^\/\?]+)/,
    /snd\.sc\/([a-zA-Z0-9]+)/,
  ];
  
  for (const pattern of patterns) {
    const match = url.match(pattern);
    if (match) {
      return match[0];
    }
  }
  return null;
};

/**
 * Fetches track information from SoundCloud API
 */
const fetchSoundCloudTrackInfo = async (url: string): Promise<SoundCloudTrackInfo> => {
  try {
    const resolveUrl = `${SOUNDCLOUD_API_BASE}/resolve?url=${encodeURIComponent(url)}&client_id=${SOUNDCLOUD_CLIENT_ID}`;
    const response = await fetch(resolveUrl);
    
    if (!response.ok) {
      throw new Error('Failed to fetch SoundCloud track information');
    }
    
    return await response.json();
  } catch (error) {
    console.warn('SoundCloud API not available, using mock data');
    // Return mock data for demonstration
    return {
      id: 'mock-track-id',
      title: 'Sample DJ Set',
      user: { username: 'Sample DJ' },
      duration: 3600000, // 1 hour in milliseconds
      stream_url: 'mock-stream-url',
      waveform_url: 'mock-waveform-url',
      artwork_url: 'mock-artwork-url',
      genre: 'Electronic',
      tag_list: 'house techno electronic',
    };
  }
};

/**
 * Simulates audio analysis for tempo detection
 */
const analyzeTempoAndKey = async (audioData: ArrayBuffer, progressCallback?: (progress: number) => void): Promise<{ tempo: number; key: string }> => {
  // Simulate processing time
  for (let i = 0; i <= 100; i += 10) {
    await new Promise(resolve => setTimeout(resolve, 100));
    progressCallback?.(i);
  }
  
  // Mock tempo and key detection
  const tempos = [120, 123, 125, 126, 128, 130, 132, 135];
  const keys = ['Am', 'Bm', 'Cm', 'Dm', 'Em', 'F#m', 'Gm', 'C#m'];
  
  return {
    tempo: tempos[Math.floor(Math.random() * tempos.length)],
    key: keys[Math.floor(Math.random() * keys.length)],
  };
};

/**
 * Simulates track identification within a DJ set
 */
const identifyTracks = async (audioData: ArrayBuffer, duration: number, progressCallback?: (progress: number) => void): Promise<Track[]> => {
  // Simulate track identification process
  const tracks: Track[] = [];
  const numTracks = Math.floor(duration / 300) + Math.floor(Math.random() * 3) + 2; // 2-5 tracks
  
  for (let i = 0; i < numTracks; i++) {
    const progress = (i / numTracks) * 100;
    progressCallback?.(progress);
    
    // Simulate processing time
    await new Promise(resolve => setTimeout(resolve, 500));
    
    const startTime = i * (duration / numTracks);
    const endTime = (i + 1) * (duration / numTracks);
    const trackDuration = endTime - startTime;
    
    const mockTrack = MOCK_TRACKS[i % MOCK_TRACKS.length];
    const { tempo, key } = await analyzeTempoAndKey(audioData);
    
    tracks.push({
      ...mockTrack,
      id: `track-${i + 1}`,
      startTime,
      endTime,
      duration: trackDuration,
      tempo,
      key,
      confidence: 0.8 + Math.random() * 0.2, // 80-100% confidence
    });
  }
  
  return tracks;
};

/**
 * Downloads audio data from SoundCloud (simulated)
 */
const downloadAudioData = async (streamUrl: string, progressCallback?: (progress: number) => void): Promise<ArrayBuffer> => {
  // Simulate download progress
  for (let i = 0; i <= 100; i += 5) {
    await new Promise(resolve => setTimeout(resolve, 50));
    progressCallback?.(i);
  }
  
  // Return mock audio data
  return new ArrayBuffer(1024 * 1024); // 1MB mock data
};

/**
 * Main function to analyze a SoundCloud DJ set
 */
export const analyzeSoundCloudSet = async (
  url: string,
  progressCallback?: (progress: number) => void
): Promise<Track[]> => {
  try {
    // Step 1: Fetch SoundCloud track info (0-20%)
    progressCallback?.(5);
    const trackInfo = await fetchSoundCloudTrackInfo(url);
    progressCallback?.(20);
    
    // Step 2: Download audio data (20-40%)
    const audioData = await downloadAudioData(trackInfo.stream_url, (progress) => {
      progressCallback?.(20 + (progress * 0.2));
    });
    progressCallback?.(40);
    
    // Step 3: Identify tracks (40-80%)
    const duration = trackInfo.duration / 1000; // Convert to seconds
    const tracks = await identifyTracks(audioData, duration, (progress) => {
      progressCallback?.(40 + (progress * 0.4));
    });
    progressCallback?.(80);
    
    // Step 4: Final processing (80-100%)
    await new Promise(resolve => setTimeout(resolve, 1000));
    progressCallback?.(100);
    
    return tracks;
  } catch (error) {
    console.error('Analysis failed:', error);
    throw new Error('Failed to analyze SoundCloud set. Please check the URL and try again.');
  }
};

/**
 * Export tracks to various formats
 */
export const exportTracks = (tracks: Track[], format: 'json' | 'csv' | 'txt' = 'json'): string => {
  switch (format) {
    case 'csv':
      const csvHeader = 'Track,Artist,Tempo,Key,Start Time,End Time,Duration,Confidence\n';
      const csvRows = tracks.map(track => 
        `"${track.songName}","${track.artistName}",${track.tempo},"${track.key}",${track.startTime},${track.endTime},${track.duration},${track.confidence}`
      ).join('\n');
      return csvHeader + csvRows;
    
    case 'txt':
      return tracks.map((track, index) => 
        `${index + 1}. ${track.songName} - ${track.artistName} (${track.tempo} BPM, ${track.key})`
      ).join('\n');
    
    default:
      return JSON.stringify(tracks, null, 2);
  }
};
