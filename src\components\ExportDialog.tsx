import React, { useState } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  FormControl,
  FormLabel,
  RadioGroup,
  FormControlLabel,
  Radio,
  TextField,
  Box,
  Typography,
  IconButton,
  Snackbar,
  Alert,
} from '@mui/material';
import { Close, Download, ContentCopy } from '@mui/icons-material';
import { Track } from '../types/Track';
import { exportTracks } from '../services/audioAnalysisService';

interface ExportDialogProps {
  open: boolean;
  onClose: () => void;
  tracks: Track[];
}

const ExportDialog: React.FC<ExportDialogProps> = ({ open, onClose, tracks }) => {
  const [format, setFormat] = useState<'json' | 'csv' | 'txt'>('json');
  const [filename, setFilename] = useState('dj-set-tracks');
  const [showCopySuccess, setShowCopySuccess] = useState(false);

  const handleExport = () => {
    const data = exportTracks(tracks, format);
    const blob = new Blob([data], { type: getContentType(format) });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `${filename}.${format}`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
    onClose();
  };

  const handleCopyToClipboard = async () => {
    const data = exportTracks(tracks, format);
    try {
      await navigator.clipboard.writeText(data);
      setShowCopySuccess(true);
    } catch (err) {
      console.error('Failed to copy to clipboard:', err);
    }
  };

  const getContentType = (format: string): string => {
    switch (format) {
      case 'csv':
        return 'text/csv';
      case 'txt':
        return 'text/plain';
      default:
        return 'application/json';
    }
  };

  const getPreviewData = (): string => {
    const data = exportTracks(tracks.slice(0, 2), format); // Show preview of first 2 tracks
    return data.length > 500 ? data.substring(0, 500) + '...' : data;
  };

  return (
    <>
      <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
        <DialogTitle>
          Export Track List
          <IconButton
            aria-label="close"
            onClick={onClose}
            sx={{ position: 'absolute', right: 8, top: 8 }}
          >
            <Close />
          </IconButton>
        </DialogTitle>
        
        <DialogContent dividers>
          <Box sx={{ mb: 3 }}>
            <FormControl component="fieldset">
              <FormLabel component="legend">Export Format</FormLabel>
              <RadioGroup
                row
                value={format}
                onChange={(e) => setFormat(e.target.value as 'json' | 'csv' | 'txt')}
              >
                <FormControlLabel value="json" control={<Radio />} label="JSON" />
                <FormControlLabel value="csv" control={<Radio />} label="CSV" />
                <FormControlLabel value="txt" control={<Radio />} label="Text" />
              </RadioGroup>
            </FormControl>
          </Box>

          <TextField
            fullWidth
            label="Filename"
            value={filename}
            onChange={(e) => setFilename(e.target.value)}
            sx={{ mb: 3 }}
            helperText={`File will be saved as ${filename}.${format}`}
          />

          <Box>
            <Typography variant="subtitle2" gutterBottom>
              Preview ({tracks.length} tracks total):
            </Typography>
            <TextField
              fullWidth
              multiline
              rows={8}
              value={getPreviewData()}
              InputProps={{
                readOnly: true,
                style: { fontFamily: 'monospace', fontSize: '0.875rem' },
              }}
              variant="outlined"
            />
          </Box>
        </DialogContent>

        <DialogActions>
          <Button onClick={handleCopyToClipboard} startIcon={<ContentCopy />}>
            Copy to Clipboard
          </Button>
          <Button onClick={onClose}>Cancel</Button>
          <Button
            onClick={handleExport}
            variant="contained"
            startIcon={<Download />}
          >
            Download
          </Button>
        </DialogActions>
      </Dialog>

      <Snackbar
        open={showCopySuccess}
        autoHideDuration={3000}
        onClose={() => setShowCopySuccess(false)}
      >
        <Alert onClose={() => setShowCopySuccess(false)} severity="success">
          Track list copied to clipboard!
        </Alert>
      </Snackbar>
    </>
  );
};

export default ExportDialog;
