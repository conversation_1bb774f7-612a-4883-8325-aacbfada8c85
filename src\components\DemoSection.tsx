import React from 'react';
import {
  <PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON>,
  Divider,
} from '@mui/material';
import {
  PlayArrow,
  TrendingUp,
  MusicNote,
  Timeline,
  Equalizer,
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import { Track } from '../types/Track';

interface DemoSectionProps {
  onLoadDemo: (tracks: Track[]) => void;
}

const DEMO_TRACKS: Track[] = [
  {
    id: 'demo-1',
    songName: 'Midnight City',
    artistName: 'M83',
    tempo: 128,
    key: 'F#m',
    startTime: 0,
    endTime: 240,
    duration: 240,
    confidence: 0.92,
    genre: 'Electronic',
    energy: 0.85,
    danceability: 0.78,
  },
  {
    id: 'demo-2',
    songName: 'One More Time',
    artistName: 'Daft Punk',
    tempo: 123,
    key: 'Em',
    startTime: 240,
    endTime: 480,
    duration: 240,
    confidence: 0.88,
    genre: 'House',
    energy: 0.92,
    danceability: 0.95,
  },
  {
    id: 'demo-3',
    songName: 'Strobe',
    artistName: 'Deadmau5',
    tempo: 128,
    key: 'C#m',
    startTime: 480,
    endTime: 840,
    duration: 360,
    confidence: 0.95,
    genre: 'Progressive House',
    energy: 0.75,
    danceability: 0.82,
  },
  {
    id: 'demo-4',
    songName: 'Levels',
    artistName: 'Avicii',
    tempo: 126,
    key: 'C#m',
    startTime: 840,
    endTime: 1080,
    duration: 240,
    confidence: 0.90,
    genre: 'Progressive House',
    energy: 0.88,
    danceability: 0.89,
  },
];

const DemoSection: React.FC<DemoSectionProps> = ({ onLoadDemo }) => {
  const features = [
    {
      icon: <MusicNote />,
      title: 'Track Identification',
      description: 'Automatically identify individual tracks within DJ sets',
    },
    {
      icon: <Timeline />,
      title: 'Tempo Analysis',
      description: 'Detect BPM (beats per minute) for each track',
    },
    {
      icon: <Equalizer />,
      title: 'Key Detection',
      description: 'Identify musical keys and harmonies',
    },
    {
      icon: <TrendingUp />,
      title: 'Mix Analytics',
      description: 'Analyze energy levels, transitions, and mix quality',
    },
  ];

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.8 }}
    >
      <Paper elevation={3} sx={{ p: 4, mb: 4 }}>
        <Typography variant="h4" align="center" gutterBottom>
          Welcome to DJ Set Analyzer
        </Typography>
        <Typography variant="body1" align="center" color="text.secondary" sx={{ mb: 4 }}>
          Experience the power of AI-driven music analysis. Try our demo to see how we identify tracks,
          detect tempo, and analyze musical keys in DJ sets.
        </Typography>

        <Grid container spacing={3} sx={{ mb: 4 }}>
          {features.map((feature, index) => (
            <Grid item xs={12} sm={6} md={3} key={index}>
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 + 0.3 }}
              >
                <Card elevation={2} sx={{ height: '100%', textAlign: 'center' }}>
                  <CardContent>
                    <Box sx={{ color: 'primary.main', mb: 2 }}>
                      {feature.icon}
                    </Box>
                    <Typography variant="h6" gutterBottom>
                      {feature.title}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {feature.description}
                    </Typography>
                  </CardContent>
                </Card>
              </motion.div>
            </Grid>
          ))}
        </Grid>

        <Divider sx={{ my: 4 }} />

        <Box sx={{ textAlign: 'center' }}>
          <Typography variant="h5" gutterBottom>
            Try the Demo
          </Typography>
          <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
            Load sample data to explore the interface and see how track analysis works
          </Typography>
          
          <Button
            variant="contained"
            size="large"
            startIcon={<PlayArrow />}
            onClick={() => onLoadDemo(DEMO_TRACKS)}
            sx={{
              px: 4,
              py: 1.5,
              borderRadius: 3,
              background: 'linear-gradient(45deg, #dc004e 30%, #f48fb1 90%)',
              '&:hover': {
                background: 'linear-gradient(45deg, #ad1457 30%, #dc004e 90%)',
              },
            }}
          >
            Load Demo Data
          </Button>
        </Box>

        <Box sx={{ mt: 4 }}>
          <Typography variant="h6" gutterBottom>
            Demo Track List Preview:
          </Typography>
          <Stack spacing={1}>
            {DEMO_TRACKS.map((track, index) => (
              <Box
                key={track.id}
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'space-between',
                  p: 2,
                  bgcolor: 'background.paper',
                  borderRadius: 1,
                  border: '1px solid',
                  borderColor: 'divider',
                }}
              >
                <Box>
                  <Typography variant="body1" fontWeight={600}>
                    {index + 1}. {track.songName}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {track.artistName}
                  </Typography>
                </Box>
                <Stack direction="row" spacing={1}>
                  <Chip label={`${track.tempo} BPM`} size="small" color="primary" />
                  <Chip label={track.key} size="small" color="secondary" />
                  <Chip label={track.genre} size="small" variant="outlined" />
                </Stack>
              </Box>
            ))}
          </Stack>
        </Box>
      </Paper>
    </motion.div>
  );
};

export default DemoSection;
