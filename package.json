{"name": "soundcloud-dj-analyzer", "version": "1.0.0", "description": "A modern React web application for analyzing SoundCloud DJ sets with track identification, tempo and key detection", "private": true, "dependencies": {"@types/node": "^16.18.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-scripts": "5.0.1", "typescript": "^4.9.0", "web-vitals": "^2.1.0", "@emotion/react": "^11.11.0", "@emotion/styled": "^11.11.0", "@mui/material": "^5.14.0", "@mui/icons-material": "^5.14.0", "@mui/lab": "^5.0.0-alpha.140", "axios": "^1.5.0", "react-router-dom": "^6.15.0", "framer-motion": "^10.16.0", "recharts": "^2.8.0", "react-dropzone": "^14.2.0", "tone": "^14.7.0", "wavesurfer.js": "^7.3.0", "music-tempo": "^0.3.0", "key-finder": "^1.0.0"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "@types/jest": "^27.5.0", "eslint": "^8.48.0", "@typescript-eslint/eslint-plugin": "^6.7.0", "@typescript-eslint/parser": "^6.7.0"}, "proxy": "http://localhost:3001"}