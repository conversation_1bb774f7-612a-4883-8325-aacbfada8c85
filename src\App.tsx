import React, { useState } from 'react';
import {
  Container,
  AppBar,
  <PERSON><PERSON><PERSON>,
  <PERSON>po<PERSON>,
  Switch,
  FormControlLabel,
  Box,
  Paper,
  Fade,
} from '@mui/material';
import { Brightness4, Brightness7, MusicNote } from '@mui/icons-material';
import { useTheme } from './contexts/ThemeContext';
import SoundCloudInput from './components/SoundCloudInput';
import TrackList from './components/TrackList';
import AnalysisProgress from './components/AnalysisProgress';
import DemoSection from './components/DemoSection';
import { Track } from './types/Track';

function App() {
  const { isDarkMode, toggleTheme } = useTheme();
  const [tracks, setTracks] = useState<Track[]>([]);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [analysisProgress, setAnalysisProgress] = useState(0);

  const handleAnalysisStart = () => {
    setIsAnalyzing(true);
    setAnalysisProgress(0);
    setTracks([]);
  };

  const handleAnalysisComplete = (analyzedTracks: Track[]) => {
    setTracks(analyzedTracks);
    setIsAnalyzing(false);
    setAnalysisProgress(100);
  };

  const handleProgressUpdate = (progress: number) => {
    setAnalysisProgress(progress);
  };

  const handleLoadDemo = (demoTracks: Track[]) => {
    setTracks(demoTracks);
    setIsAnalyzing(false);
    setAnalysisProgress(100);
  };

  return (
    <Box sx={{ flexGrow: 1, minHeight: '100vh' }}>
      <AppBar position="static" elevation={0}>
        <Toolbar>
          <MusicNote sx={{ mr: 2 }} />
          <Typography variant="h6" component="div" sx={{ flexGrow: 1 }}>
            SoundCloud DJ Set Analyzer
          </Typography>
          <FormControlLabel
            control={
              <Switch
                checked={isDarkMode}
                onChange={toggleTheme}
                icon={<Brightness7 />}
                checkedIcon={<Brightness4 />}
              />
            }
            label=""
          />
        </Toolbar>
      </AppBar>

      <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
        <Fade in timeout={800}>
          <Paper
            elevation={3}
            sx={{
              p: 4,
              mb: 4,
              background: isDarkMode
                ? 'linear-gradient(135deg, #1e1e1e 0%, #2d2d2d 100%)'
                : 'linear-gradient(135deg, #ffffff 0%, #f5f5f5 100%)',
            }}
          >
            <Typography
              variant="h3"
              component="h1"
              gutterBottom
              align="center"
              sx={{
                background: isDarkMode
                  ? 'linear-gradient(45deg, #90caf9 30%, #f48fb1 90%)'
                  : 'linear-gradient(45deg, #1976d2 30%, #dc004e 90%)',
                backgroundClip: 'text',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
                mb: 2,
              }}
            >
              DJ Set Analyzer
            </Typography>
            <Typography
              variant="h6"
              align="center"
              color="text.secondary"
              sx={{ mb: 4 }}
            >
              Analyze SoundCloud DJ sets to identify tracks, detect tempo, and find musical keys
            </Typography>

            <SoundCloudInput
              onAnalysisStart={handleAnalysisStart}
              onAnalysisComplete={handleAnalysisComplete}
              onProgressUpdate={handleProgressUpdate}
            />
          </Paper>
        </Fade>

        {isAnalyzing && (
          <Fade in timeout={500}>
            <Box sx={{ mb: 4 }}>
              <AnalysisProgress progress={analysisProgress} />
            </Box>
          </Fade>
        )}

        {tracks.length > 0 ? (
          <Fade in timeout={1000}>
            <Box>
              <TrackList tracks={tracks} />
            </Box>
          </Fade>
        ) : !isAnalyzing && (
          <Fade in timeout={1000}>
            <Box>
              <DemoSection onLoadDemo={handleLoadDemo} />
            </Box>
          </Fade>
        )}
      </Container>
    </Box>
  );
}

export default App;
