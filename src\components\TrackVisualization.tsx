import React from 'react';
import { Box, Typography } from '@mui/material';
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  BarChart,
  Bar,
} from 'recharts';
import { Track } from '../types/Track';

interface TrackVisualizationProps {
  track: Track;
}

const TrackVisualization: React.FC<TrackVisualizationProps> = ({ track }) => {
  // Generate mock waveform data for visualization
  const generateWaveformData = (duration: number) => {
    const points = Math.min(100, Math.max(20, Math.floor(duration / 2)));
    return Array.from({ length: points }, (_, i) => ({
      time: (i / points) * duration,
      amplitude: Math.random() * 0.8 + 0.1,
      energy: Math.random() * 100,
    }));
  };

  // Generate tempo variation data
  const generateTempoData = (baseTempo: number, duration: number) => {
    const points = Math.floor(duration / 10); // One point every 10 seconds
    return Array.from({ length: points }, (_, i) => ({
      time: i * 10,
      tempo: baseTempo + (Math.random() - 0.5) * 10, // ±5 BPM variation
    }));
  };

  const waveformData = generateWaveformData(track.duration);
  const tempoData = generateTempoData(track.tempo, track.duration);

  const formatTime = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  return (
    <Box sx={{ mt: 2 }}>
      <Typography variant="h6" gutterBottom>
        Audio Visualization
      </Typography>
      
      <Box sx={{ mb: 3 }}>
        <Typography variant="subtitle2" gutterBottom>
          Waveform & Energy
        </Typography>
        <ResponsiveContainer width="100%" height={150}>
          <LineChart data={waveformData}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis
              dataKey="time"
              tickFormatter={formatTime}
              type="number"
              scale="linear"
              domain={[0, track.duration]}
            />
            <YAxis domain={[0, 1]} />
            <Tooltip
              labelFormatter={(value) => `Time: ${formatTime(Number(value))}`}
              formatter={(value: number, name: string) => [
                name === 'amplitude' ? `${(value * 100).toFixed(1)}%` : `${value.toFixed(1)}%`,
                name === 'amplitude' ? 'Amplitude' : 'Energy'
              ]}
            />
            <Line
              type="monotone"
              dataKey="amplitude"
              stroke="#1976d2"
              strokeWidth={2}
              dot={false}
              name="amplitude"
            />
            <Line
              type="monotone"
              dataKey="energy"
              stroke="#dc004e"
              strokeWidth={1}
              dot={false}
              name="energy"
            />
          </LineChart>
        </ResponsiveContainer>
      </Box>

      <Box sx={{ mb: 3 }}>
        <Typography variant="subtitle2" gutterBottom>
          Tempo Variation
        </Typography>
        <ResponsiveContainer width="100%" height={120}>
          <BarChart data={tempoData}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis
              dataKey="time"
              tickFormatter={formatTime}
            />
            <YAxis
              domain={[track.tempo - 15, track.tempo + 15]}
              tickFormatter={(value) => `${value} BPM`}
            />
            <Tooltip
              labelFormatter={(value) => `Time: ${formatTime(Number(value))}`}
              formatter={(value: number) => [`${value.toFixed(1)} BPM`, 'Tempo']}
            />
            <Bar
              dataKey="tempo"
              fill="#42a5f5"
              radius={[2, 2, 0, 0]}
            />
          </BarChart>
        </ResponsiveContainer>
      </Box>

      {track.waveformUrl && (
        <Box sx={{ mb: 2 }}>
          <Typography variant="subtitle2" gutterBottom>
            SoundCloud Waveform
          </Typography>
          <Box
            sx={{
              width: '100%',
              height: 60,
              background: `url(${track.waveformUrl}) no-repeat center`,
              backgroundSize: 'contain',
              border: '1px solid',
              borderColor: 'divider',
              borderRadius: 1,
            }}
          />
        </Box>
      )}

      <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
        <Box sx={{ minWidth: 120 }}>
          <Typography variant="caption" color="text.secondary">
            Key Signature
          </Typography>
          <Typography variant="h6" color="primary">
            {track.key}
          </Typography>
        </Box>
        <Box sx={{ minWidth: 120 }}>
          <Typography variant="caption" color="text.secondary">
            BPM
          </Typography>
          <Typography variant="h6" color="secondary">
            {track.tempo}
          </Typography>
        </Box>
        {track.energy && (
          <Box sx={{ minWidth: 120 }}>
            <Typography variant="caption" color="text.secondary">
              Energy Level
            </Typography>
            <Typography variant="h6" color="success.main">
              {Math.round(track.energy * 100)}%
            </Typography>
          </Box>
        )}
        {track.danceability && (
          <Box sx={{ minWidth: 120 }}>
            <Typography variant="caption" color="text.secondary">
              Danceability
            </Typography>
            <Typography variant="h6" color="warning.main">
              {Math.round(track.danceability * 100)}%
            </Typography>
          </Box>
        )}
      </Box>
    </Box>
  );
};

export default TrackVisualization;
