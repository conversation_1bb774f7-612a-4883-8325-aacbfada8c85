import React from 'react';
import {
  Box,
  Paper,
  LinearProgress,
  Typo<PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  StepContent,
} from '@mui/material';
import { motion } from 'framer-motion';

interface AnalysisProgressProps {
  progress: number;
}

const AnalysisProgress: React.FC<AnalysisProgressProps> = ({ progress }) => {
  const steps = [
    {
      label: 'Fetching SoundCloud Data',
      description: 'Retrieving track information and audio stream',
      threshold: 20,
    },
    {
      label: 'Audio Processing',
      description: 'Downloading and processing audio data',
      threshold: 40,
    },
    {
      label: 'Track Identification',
      description: 'Identifying individual tracks in the mix',
      threshold: 60,
    },
    {
      label: 'Tempo Analysis',
      description: 'Analyzing BPM for each identified track',
      threshold: 80,
    },
    {
      label: 'Key Detection',
      description: 'Detecting musical keys and harmonies',
      threshold: 100,
    },
  ];

  const activeStep = steps.findIndex(step => progress < step.threshold);
  const currentStep = activeStep === -1 ? steps.length - 1 : activeStep;

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <Paper elevation={3} sx={{ p: 3 }}>
        <Typography variant="h6" gutterBottom align="center">
          Analyzing DJ Set
        </Typography>
        
        <Box sx={{ mb: 3 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
            <Box sx={{ width: '100%', mr: 1 }}>
              <LinearProgress
                variant="determinate"
                value={progress}
                sx={{
                  height: 8,
                  borderRadius: 4,
                  '& .MuiLinearProgress-bar': {
                    borderRadius: 4,
                    background: 'linear-gradient(45deg, #1976d2 30%, #42a5f5 90%)',
                  },
                }}
              />
            </Box>
            <Box sx={{ minWidth: 35 }}>
              <Typography variant="body2" color="text.secondary">
                {Math.round(progress)}%
              </Typography>
            </Box>
          </Box>
        </Box>

        <Stepper activeStep={currentStep} orientation="vertical">
          {steps.map((step, index) => (
            <Step key={step.label}>
              <StepLabel>
                <Typography variant="body1" fontWeight={index === currentStep ? 600 : 400}>
                  {step.label}
                </Typography>
              </StepLabel>
              <StepContent>
                <Typography variant="body2" color="text.secondary">
                  {step.description}
                </Typography>
              </StepContent>
            </Step>
          ))}
        </Stepper>

        {progress === 100 && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.5 }}
          >
            <Typography
              variant="body1"
              align="center"
              color="success.main"
              sx={{ mt: 2, fontWeight: 600 }}
            >
              Analysis Complete! 🎉
            </Typography>
          </motion.div>
        )}
      </Paper>
    </motion.div>
  );
};

export default AnalysisProgress;
